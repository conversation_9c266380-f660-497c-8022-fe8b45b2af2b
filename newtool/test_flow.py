#!/usr/bin/env python3
"""
Test script to verify the URL processing and analysis flow.
"""

import json
import os
import sys
from typing import List

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.request import UrlDepthItem, WebsiteHealthAnalysisRequest
from utils.url_processor import process_urls
from services.reachability_service import check_url_reachability
from services.health_analysis_service import analyze_website_health
from utils.logger import get_logger

logger = get_logger(__name__)

def test_url_processing_flow():
    """
    Test the complete URL processing flow with sample data.
    """
    logger.info("Starting URL processing flow test...")
    
    # Sample test data
    sample_urls = [
        UrlDepthItem(
            url_depth=1,
            urls=[
                "https://www.google.com",
                "https://www.github.com",
                "https://www.stackoverflow.com",
                "https://www.python.org",
                "https://www.fastapi.tiangolo.com"
            ]
        ),
        UrlDepthItem(
            url_depth=2,
            urls=[
                "https://docs.python.org",
                "https://www.wikipedia.org",
                "https://www.reddit.com",
                "https://www.youtube.com",
                "https://www.linkedin.com"
            ]
        )
    ]
    
    home_page_url = "https://www.example.com"
    
    try:
        # Step 1: Process URLs (dedupe, sort, take top 100, convert to dict)
        logger.info("Step 1: Processing URLs...")
        url_dict = process_urls(sample_urls)
        logger.info(f"Processed {len(url_dict)} unique URLs")
        logger.info(f"URL dictionary: {url_dict}")
        
        # Step 2: Check reachability (send 10 URLs at once, iterate until 4 reachable)
        logger.info("Step 2: Checking URL reachability...")
        reachable_urls = check_url_reachability(url_dict, target_reachable_count=4)
        logger.info(f"Found {len(reachable_urls)} reachable URLs: {reachable_urls}")
        
        # Step 3: Analyze website health (dedupe final list, pass 1 URL at a time)
        logger.info("Step 3: Analyzing website health...")
        analysis_results = analyze_website_health(reachable_urls, home_page_url)
        logger.info(f"Health analysis completed for {len(analysis_results)} URLs")
        
        # Log final results
        logger.info("=== FINAL RESULTS ===")
        for i, result in enumerate(analysis_results):
            logger.info(f"Result {i+1}: {json.dumps(result, indent=2)}")
        
        return {
            "status": "success",
            "processed_urls": len(url_dict),
            "reachable_urls": len(reachable_urls),
            "analysis_results": len(analysis_results),
            "results": analysis_results
        }
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)
        return {
            "status": "error",
            "error": str(e)
        }

def test_gemini_client():
    """
    Test the Gemini client with a simple request.
    """
    logger.info("Testing Gemini client...")
    
    try:
        from utils.gemini_client import GeminiClient
        
        client = GeminiClient()
        response = client.make_request("Hello, please respond with 'Gemini 2.5 Flash is working!'")
        
        if response:
            logger.info(f"Gemini client test successful: {response}")
            return {"status": "success", "response": response}
        else:
            logger.error("Gemini client test failed: No response")
            return {"status": "error", "error": "No response from Gemini"}
            
    except Exception as e:
        logger.error(f"Gemini client test failed: {e}", exc_info=True)
        return {"status": "error", "error": str(e)}

if __name__ == "__main__":
    print("=" * 50)
    print("Testing Gemini Client")
    print("=" * 50)
    gemini_result = test_gemini_client()
    print(json.dumps(gemini_result, indent=2))
    
    print("\n" + "=" * 50)
    print("Testing Complete URL Processing Flow")
    print("=" * 50)
    flow_result = test_url_processing_flow()
    print(json.dumps(flow_result, indent=2))
