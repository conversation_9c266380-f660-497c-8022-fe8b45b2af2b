import os
import time
from google import genai
from google.genai.types import Too<PERSON>, GenerateContentConfig, UrlContext
from utils.logger import get_logger

logger = get_logger(__name__)

class GeminiClient:
    def __init__(self, api_key: str = None):
        # Configure API key
        if api_key:
            self.api_key = api_key
        else:
            # If no API key is provided, configure it through environment variables
            self.api_key = os.getenv('GEMINI_API_KEY')
            if not self.api_key:
                logger.warning("No Gemini API key provided. Please set GEMINI_API_KEY environment variable.")

        # Initialize the client with the new SDK
        self.client = genai.Client(api_key=self.api_key)
        self.model_id = "gemini-2.5-flash"

        # Configure URL context tool
        self.url_context_tool = Tool(url_context=UrlContext)

    def make_request(self, prompt: str, url: str = None, use_url_context: bool = True, max_retries: int = 3) -> str:
        """
        Makes a request to the Gemini API with the given prompt using the new SDK.

        Args:
            prompt: The prompt to send to the language model.
            url: Optional URL for context when analyzing specific websites.
            use_url_context: Whether to enable URL context tool (default: True).
            max_retries: Maximum number of retry attempts (default: 3).

        Returns:
            The response from the language model as a string.
        """
        for attempt in range(max_retries + 1):
            try:
                logger.info(f"Making request to Gemini 2.5 Flash API (attempt {attempt + 1}/{max_retries + 1})...")
                if url:
                    logger.info(f"Including URL context: {url}")

                # Configure the request with optional URL context tool
                tools = [self.url_context_tool] if use_url_context else []
                config = GenerateContentConfig(
                    tools=tools,
                    response_modalities=["TEXT"],
                )

                # Generate content with the new SDK
                response = self.client.models.generate_content(
                    model=self.model_id,
                    contents=prompt,
                    config=config
                )

                if response and response.candidates and len(response.candidates) > 0:
                    # Extract text from response parts
                    response_text = ""
                    for part in response.candidates[0].content.parts:
                        if hasattr(part, 'text') and part.text:
                            response_text += part.text

                    if response_text:
                        logger.info(f"Received response from Gemini API (length: {len(response_text)} characters)")
                        logger.debug(f"Full response: {response_text}")

                        # Log URL context metadata if available
                        if hasattr(response.candidates[0], 'url_context_metadata') and response.candidates[0].url_context_metadata:
                            url_metadata = response.candidates[0].url_context_metadata
                            if hasattr(url_metadata, 'url_metadata') and url_metadata.url_metadata:
                                successful_urls = []
                                failed_urls = []
                                for metadata in url_metadata.url_metadata:
                                    if hasattr(metadata, 'url_retrieval_status'):
                                        if 'SUCCESS' in str(metadata.url_retrieval_status):
                                            successful_urls.append(metadata.retrieved_url)
                                        else:
                                            failed_urls.append(metadata.retrieved_url)
                                
                                logger.info(f"URL retrieval summary - Successful: {len(successful_urls)}, Failed: {len(failed_urls)}")
                                if failed_urls:
                                    logger.debug(f"Failed URLs: {failed_urls}")
                                if successful_urls:
                                    logger.debug(f"Successful URLs: {successful_urls}")
                            else:
                                logger.debug(f"URL context metadata: {url_metadata}")

                        return response_text
                    else:
                        logger.warning("Received empty response text from Gemini API")
                        if attempt < max_retries:
                            logger.info(f"Retrying in 2 seconds...")
                            time.sleep(2)
                            continue
                        return None
                else:
                    logger.warning("Received empty response from Gemini API")
                    if attempt < max_retries:
                        logger.info(f"Retrying in 2 seconds...")
                        time.sleep(2)
                        continue
                    return None

            except Exception as e:
                logger.error(f"An error occurred while making a request to the Gemini API (attempt {attempt + 1}): {e}", exc_info=True)
                if attempt < max_retries:
                    logger.info(f"Retrying in 2 seconds...")
                    time.sleep(2)
                    continue
                else:
                    logger.error(f"All {max_retries + 1} attempts failed")
                    return None

        return None