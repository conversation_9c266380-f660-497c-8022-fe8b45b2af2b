import json
import re
from typing import Dict, Any
from utils.logger import get_logger

logger = get_logger(__name__)

def clean_json_response(response_text: str) -> str:
    """
    Clean JSON response by removing markdown code blocks and other formatting.
    
    Args:
        response_text: Raw response text from the API
        
    Returns:
        Cleaned JSON string
    """
    if not response_text:
        return ""
        
    cleaned_response = response_text.strip()
    
    # Remove markdown code blocks
    if cleaned_response.startswith("```json"):
        cleaned_response = cleaned_response[7:]
    elif cleaned_response.startswith("```"):
        cleaned_response = cleaned_response[3:]
        
    if cleaned_response.endswith("```"):
        cleaned_response = cleaned_response[:-3]
    
    # Remove any leading/trailing whitespace
    cleaned_response = cleaned_response.strip()
    
    # Try to extract JSO<PERSON> from text if it's embedded
    json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
    if json_match:
        cleaned_response = json_match.group(0)
    
    return cleaned_response

def parse_json_response(response_text: str, context: str = "") -> Dict[Any, Any]:
    """
    Parse JSON response with error handling and cleaning.
    
    Args:
        response_text: Raw response text from the API
        context: Context for logging (e.g., "reachability check", "health analysis")
        
    Returns:
        Parsed JSON dictionary, or empty dict if parsing fails
    """
    if not response_text:
        logger.warning(f"Empty response received for {context}")
        return {}
    
    try:
        cleaned_response = clean_json_response(response_text)
        if not cleaned_response:
            logger.warning(f"No JSON content found in response for {context}")
            return {}
            
        response_json = json.loads(cleaned_response)
        logger.debug(f"Successfully parsed JSON response for {context}")
        return response_json
        
    except json.JSONDecodeError as e:
        logger.error(f"Failed to decode JSON from API response for {context}: {e}")
        logger.error(f"Raw response: {response_text}")
        logger.error(f"Cleaned response: {cleaned_response if 'cleaned_response' in locals() else 'N/A'}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error parsing JSON for {context}: {e}")
        return {}