2025-08-01 18:58:29,365 - __main__ - INFO - Starting Website Health Analysis API on 0.0.0.0:8081
2025-08-01 18:58:29,365 - __main__ - INFO - Reload mode: True
INFO:     Will watch for changes in these directories: ['/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/newtool']
INFO:     Uvicorn running on http://0.0.0.0:8081 (Press CTRL+C to quit)
INFO:     Started reloader process [139850] using StatReload
INFO:     Started server process [139853]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:38566 - "GET / HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38576 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38566 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:38566 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:38566 - "GET /health-check HTTP/1.1" 200 OK
2025-08-01 18:59:00,478 - api.main - INFO - Received request for website: https://drynotch.com (RefID: 208378df-cf32-4b91-aaea-f15f9cfeddcd)
2025-08-01 18:59:00,478 - api.main - INFO - Step 1: Processing URLs...
2025-08-01 18:59:00,478 - utils.url_processor - INFO - Processing URLs from 1 depth items
2025-08-01 18:59:00,478 - utils.url_processor - INFO - Total URLs extracted: 48
2025-08-01 18:59:00,479 - utils.url_processor - INFO - Valid URLs after filtering: 48
2025-08-01 18:59:00,479 - utils.url_processor - INFO - Unique URLs after deduplication: 48
2025-08-01 18:59:00,479 - utils.url_processor - INFO - URLs after sorting by length and taking top 100: 48
2025-08-01 18:59:00,479 - api.main - INFO - Processed 48 unique URLs from 48 total URLs
2025-08-01 18:59:00,479 - api.main - INFO - Step 2: Checking URL reachability...
2025-08-01 18:59:00,479 - services.reachability_service - INFO - Processing batch of 10 URLs.
2025-08-01 18:59:00,479 - utils.gemini_client - INFO - Making request to Gemini 2.5 Flash API (attempt 1/4)...
2025-08-01 18:59:10,985 - utils.gemini_client - INFO - Received response from Gemini API (length: 59 characters)
2025-08-01 18:59:10,985 - utils.gemini_client - INFO - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://drynotch.com',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/cdn',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/cart',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/pages/faqs',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/collections',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/account/login',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/pages/contact',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/pages/about-us',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/pages/shipping',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/pages/wishlist',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-01 18:59:10,985 - services.reachability_service - INFO - Received reachability response: ```json
{
    "reachable_urls": [0, 3, 4, 6, 7, 8, 9]
}
```
2025-08-01 18:59:10,985 - services.reachability_service - ERROR - Failed to decode JSON from Gemini API response: Expecting value: line 1 column 1 (char 0)
2025-08-01 18:59:10,985 - services.reachability_service - ERROR - Raw response: ```json
{
    "reachable_urls": [0, 3, 4, 6, 7, 8, 9]
}
```
2025-08-01 18:59:10,985 - services.reachability_service - INFO - Processing batch of 10 URLs.
2025-08-01 18:59:10,985 - utils.gemini_client - INFO - Making request to Gemini 2.5 Flash API (attempt 1/4)...
2025-08-01 18:59:19,797 - utils.gemini_client - INFO - Received response from Gemini API (length: 62 characters)
2025-08-01 18:59:19,798 - utils.gemini_client - INFO - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://drynotch.com/collections/all',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/account/register',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/pages/size-chart',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/collections/top-wear',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/collections/crop-tops',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/collections/frontpage',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/policies/refund-policy',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/collections/bottom-wear',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/collections/co-ord-sets',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/policies/privacy-policy',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
)]
2025-08-01 18:59:19,798 - services.reachability_service - INFO - Received reachability response: ```json
{
    "reachable_urls": [10, 13, 14, 15, 17, 18]
}
```
2025-08-01 18:59:19,798 - services.reachability_service - ERROR - Failed to decode JSON from Gemini API response: Expecting value: line 1 column 1 (char 0)
2025-08-01 18:59:19,798 - services.reachability_service - ERROR - Raw response: ```json
{
    "reachable_urls": [10, 13, 14, 15, 17, 18]
}
```
2025-08-01 18:59:19,798 - services.reachability_service - INFO - Processing batch of 10 URLs.
2025-08-01 18:59:19,798 - utils.gemini_client - INFO - Making request to Gemini 2.5 Flash API (attempt 1/4)...
2025-08-01 18:59:27,776 - utils.gemini_client - INFO - Received response from Gemini API (length: 74 characters)
2025-08-01 18:59:27,776 - utils.gemini_client - INFO - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://drynotch.com/collections/best-sellers',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/policies/terms-of-service',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/sleek-monochrome-set',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/blue-crush-co-ord-set',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/fiery-trio-co-ord-set',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/neon-attack-co-ord-set',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/citrus-blaze-co-ord-set',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/flex-on-quarter-zip-co-ord-set',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/notchflex-neon-attack-leggings',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/work-it-out-straight-fit-pants',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-01 18:59:27,776 - services.reachability_service - INFO - Received reachability response: ```json
{
    "reachable_urls": [20, 22, 23, 24, 25, 26, 27, 28, 29]
}
```
2025-08-01 18:59:27,776 - services.reachability_service - ERROR - Failed to decode JSON from Gemini API response: Expecting value: line 1 column 1 (char 0)
2025-08-01 18:59:27,776 - services.reachability_service - ERROR - Raw response: ```json
{
    "reachable_urls": [20, 22, 23, 24, 25, 26, 27, 28, 29]
}
```
2025-08-01 18:59:27,776 - services.reachability_service - INFO - Processing batch of 10 URLs.
2025-08-01 18:59:27,776 - utils.gemini_client - INFO - Making request to Gemini 2.5 Flash API (attempt 1/4)...
2025-08-01 18:59:46,575 - utils.gemini_client - ERROR - An error occurred while making a request to the Gemini API (attempt 1): 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'Number of urls to lookup exceeds the limit (21 > 20). Please reduce the number of urls in the request.', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/newtool/utils/gemini_client.py", line 52, in make_request
    response = self.client.models.generate_content(
        model=self.model_id,
        contents=prompt,
        config=config
    )
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/google/genai/models.py", line 5778, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/google/genai/models.py", line 4716, in _generate_content
    response = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/google/genai/_api_client.py", line 1077, in request
    response = self._request(http_request, stream=False)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/google/genai/_api_client.py", line 968, in _request
    return self._retry(self._request_once, http_request, stream)  # type: ignore[no-any-return]
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/tenacity/__init__.py", line 418, in exc_check
    raise retry_exc.reraise()
          ~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/tenacity/__init__.py", line 185, in reraise
    raise self.last_attempt.result()
          ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/google/genai/_api_client.py", line 958, in _request_once
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/google/genai/errors.py", line 105, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'Number of urls to lookup exceeds the limit (21 > 20). Please reduce the number of urls in the request.', 'status': 'INVALID_ARGUMENT'}}
2025-08-01 18:59:46,578 - utils.gemini_client - INFO - Retrying in 2 seconds...
2025-08-01 18:59:48,578 - utils.gemini_client - INFO - Making request to Gemini 2.5 Flash API (attempt 2/4)...
2025-08-01 18:59:58,731 - utils.gemini_client - INFO - Received response from Gemini API (length: 40 characters)
2025-08-01 18:59:58,732 - utils.gemini_client - INFO - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://drynotch.com/products/citrus-blaze-co-ord-set#judgeme_product_reviews',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-01 18:59:58,732 - services.reachability_service - INFO - Received reachability response: ```json
{
    "reachable_urls": []
}
```
2025-08-01 18:59:58,732 - services.reachability_service - ERROR - Failed to decode JSON from Gemini API response: Expecting value: line 1 column 1 (char 0)
2025-08-01 18:59:58,732 - services.reachability_service - ERROR - Raw response: ```json
{
    "reachable_urls": []
}
```
2025-08-01 18:59:58,732 - services.reachability_service - INFO - Processing batch of 8 URLs.
2025-08-01 18:59:58,732 - utils.gemini_client - INFO - Making request to Gemini 2.5 Flash API (attempt 1/4)...
2025-08-01 19:00:10,360 - utils.gemini_client - INFO - Received response from Gemini API (length: 46 characters)
2025-08-01 19:00:10,360 - utils.gemini_client - INFO - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://drynotch.com/products/navy-nirvana-co-ord-set#judgeme_product_reviews',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/products/notchflex-dare-to-flare-leggings#judgeme_product_reviews',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Co-ords',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Leggings',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Sports+Bra',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://drynotch.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
)]
2025-08-01 19:00:10,360 - services.reachability_service - INFO - Received reachability response: ```json
{
    "reachable_urls": [40, 41]
}
```
2025-08-01 19:00:10,360 - services.reachability_service - ERROR - Failed to decode JSON from Gemini API response: Expecting value: line 1 column 1 (char 0)
2025-08-01 19:00:10,360 - services.reachability_service - ERROR - Raw response: ```json
{
    "reachable_urls": [40, 41]
}
```
2025-08-01 19:00:10,360 - services.reachability_service - INFO - Found a total of 0 reachable URLs.
2025-08-01 19:00:10,360 - api.main - INFO - Found 0 reachable URLs out of 48 processed
2025-08-01 19:00:10,360 - api.main - WARNING - No reachable URLs found
INFO:     127.0.0.1:54076 - "POST /analyze-website-health HTTP/1.1" 200 OK
WARNING:  StatReload detected changes in 'services/reachability_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [139853]
INFO:     Started server process [140282]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/health_analysis_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140282]
INFO:     Started server process [140330]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/reachability_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140330]
INFO:     Started server process [140380]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/reachability_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140380]
INFO:     Started server process [140429]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/reachability_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140429]
INFO:     Started server process [140480]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/health_analysis_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140480]
INFO:     Started server process [140520]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/health_analysis_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140520]
INFO:     Started server process [140631]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/health_analysis_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140631]
INFO:     Started server process [140683]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'utils/gemini_client.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140683]
INFO:     Started server process [140742]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/reachability_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140742]
INFO:     Started server process [140842]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/reachability_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140842]
INFO:     Started server process [140903]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'utils/gemini_client.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [140903]
INFO:     Started server process [142630]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'utils/gemini_client.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [142630]
INFO:     Started server process [142690]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/reachability_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [142690]
INFO:     Started server process [142846]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'services/reachability_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [142846]
INFO:     Started server process [143021]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
