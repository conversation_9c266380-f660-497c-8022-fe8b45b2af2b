#!/usr/bin/env python3
"""
Startup script for the Website Health Analysis API.
"""

import os
import sys
import uvicorn
from utils.logger import get_logger

logger = get_logger(__name__)

def main():
    """
    Main function to start the FastAPI server.
    """
    # Check for required environment variables
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        logger.warning("GEMINI_API_KEY environment variable not set. Please set it before running the API.")
        print("Please set the GEMINI_API_KEY environment variable:")
        print("export GEMINI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    # Configuration
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 8000))
    reload = os.getenv('RELOAD', 'true').lower() == 'true'
    
    logger.info(f"Starting Website Health Analysis API on {host}:{port}")
    logger.info(f"Reload mode: {reload}")
    
    # Start the server
    uvicorn.run(
        "api.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    main()
