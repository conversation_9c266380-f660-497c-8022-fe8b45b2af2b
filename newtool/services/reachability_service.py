from typing import Dict, List
import requests
import concurrent.futures
from utils.logger import get_logger

logger = get_logger(__name__)

def check_single_url_reachability(url: str, timeout: int = 10) -> bool:
    """
    Checks if a single URL is reachable using HTTP requests.

    Args:
        url: The URL to check.
        timeout: Request timeout in seconds.

    Returns:
        True if URL is reachable (status 200-299), False otherwise.
    """
    try:
        # Use HEAD request first (faster), fallback to GET if needed
        response = requests.head(url, timeout=timeout, allow_redirects=True)

        # If HEAD is not allowed, try GET
        if response.status_code == 405:  # Method Not Allowed
            response = requests.get(url, timeout=timeout, allow_redirects=True)

        # Consider 200-299 as successful
        is_reachable = 200 <= response.status_code < 300

        if is_reachable:
            logger.debug(f"URL reachable: {url} (status: {response.status_code})")
        else:
            logger.debug(f"URL not reachable: {url} (status: {response.status_code})")

        return is_reachable

    except requests.exceptions.RequestException as e:
        logger.debug(f"URL not reachable: {url} (error: {str(e)})")
        return False


def check_url_reachability(url_dict: Dict[int, str], target_reachable_count: int = 4) -> List[str]:
    """
    Checks the reachability of URLs using HTTP requests and returns a list of reachable URLs.

    Args:
        url_dict: A dictionary of URLs to check, with integer keys.
        target_reachable_count: The number of reachable URLs to find before stopping.

    Returns:
        A list of reachable URLs.
    """
    reachable_urls = []
    sorted_indices = sorted(url_dict.keys())

    logger.info(f"Starting reachability check for {len(url_dict)} URLs")

    # Process URLs in batches for logging purposes, but check them individually
    for i in range(0, len(sorted_indices), 10):
        if len(reachable_urls) >= target_reachable_count:
            logger.info(f"Target of {target_reachable_count} reachable URLs reached. Stopping.")
            break

        batch_indices = sorted_indices[i:i + 10]
        batch_urls = [(index, url_dict[index]) for index in batch_indices]

        logger.info(f"Processing batch of {len(batch_urls)} URLs.")

        # Use ThreadPoolExecutor for concurrent checking
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            # Submit all URLs in the batch for checking
            future_to_url = {
                executor.submit(check_single_url_reachability, url): (index, url)
                for index, url in batch_urls
            }

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_url):
                index, url = future_to_url[future]
                try:
                    is_reachable = future.result()
                    if is_reachable and len(reachable_urls) < target_reachable_count:
                        reachable_urls.append(url)
                        logger.info(f"Added reachable URL: {url}")
                except Exception as e:
                    logger.warning(f"Error checking URL {url}: {e}")

    logger.info(f"Found a total of {len(reachable_urls)} reachable URLs.")
    return reachable_urls