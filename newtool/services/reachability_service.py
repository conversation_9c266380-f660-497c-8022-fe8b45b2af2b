from typing import Dict, List
from utils.prompts import GptPromptPicker
from utils.logger import get_logger
from utils.gemini_client import GeminiClient
from utils.json_parser import parse_json_response

logger = get_logger(__name__)
gemini_client = GeminiClient()

def check_url_reachability(url_dict: Dict[int, str], target_reachable_count: int = 4) -> List[str]:
    """
    Checks the reachability of URLs in batches and returns a list of reachable URLs.

    Args:
        url_dict: A dictionary of URLs to check, with integer keys.
        target_reachable_count: The number of reachable URLs to find before stopping.

    Returns:
        A list of reachable URLs.
    """
    reachable_urls = []
    checked_indices = set()

    sorted_indices = sorted(url_dict.keys())

    for i in range(0, len(sorted_indices), 10):
        if len(reachable_urls) >= target_reachable_count:
            logger.info(f"Target of {target_reachable_count} reachable URLs reached. Stopping.")
            break

        batch_indices = sorted_indices[i:i + 10]
        batch_dict = {index: url_dict[index] for index in batch_indices if index not in checked_indices}

        if not batch_dict:
            continue

        logger.info(f"Processing batch of {len(batch_dict)} URLs.")

        prompt = GptPromptPicker.get_website_reachability_prompt(batch_dict)
        response_text = gemini_client.make_request(prompt)

        if response_text:
            logger.info(f"Received reachability response: {response_text}")
            response_json = parse_json_response(response_text, "reachability check")
            
            if response_json:
                reachable_indices = response_json.get("reachable_urls", [])
                logger.info(f"Found {len(reachable_indices)} reachable URLs in this batch: {reachable_indices}")

                for index in reachable_indices:
                    if len(reachable_urls) < target_reachable_count:
                        reachable_url = url_dict[index]
                        reachable_urls.append(reachable_url)
                        logger.info(f"Added reachable URL: {reachable_url}")
            else:
                logger.warning(f"Failed to parse JSON response for batch: {list(batch_dict.keys())}")
        else:
            logger.warning(f"No response received for batch: {list(batch_dict.keys())}")
        
        checked_indices.update(batch_dict.keys())

    logger.info(f"Found a total of {len(reachable_urls)} reachable URLs.")
    return reachable_urls