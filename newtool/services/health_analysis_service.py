import json
import re
from typing import List
from utils.prompts import GptPromptPicker
from utils.logger import get_logger
from utils.gemini_client import GeminiClient

logger = get_logger(__name__)
gemini_client = GeminiClient()

def clean_json_response(response_text: str) -> str:
    """
    Clean JSON response by removing markdown code blocks and other formatting.
    
    Args:
        response_text: Raw response text from the API
        
    Returns:
        Cleaned JSON string
    """
    cleaned_response = response_text.strip()
    
    # Remove markdown code blocks
    if cleaned_response.startswith("```json"):
        cleaned_response = cleaned_response[7:]
    elif cleaned_response.startswith("```"):
        cleaned_response = cleaned_response[3:]
        
    if cleaned_response.endswith("```"):
        cleaned_response = cleaned_response[:-3]
    
    # Remove any leading/trailing whitespace
    cleaned_response = cleaned_response.strip()
    
    # Try to extract JSON from text if it's embedded
    json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
    if json_match:
        cleaned_response = json_match.group(0)
    
    return cleaned_response

def analyze_website_health(reachable_urls: List[str], home_page_url: str) -> List[dict]:
    """
    Analyzes the health of a list of URLs and returns the analysis results.

    Args:
        reachable_urls: A list of reachable URLs.
        home_page_url: The URL of the home page.

    Returns:
        A list of dictionaries, where each dictionary contains the health analysis
        results for a single URL.
    """
    # 5. Dedupe the final list of reachable URL + home page URL
    final_urls = sorted(list(set(reachable_urls + [home_page_url])))

    analysis_results = []

    # 6. Pass just once 1 URLs at one time
    for url in final_urls:
        logger.info(f"Starting health analysis for URL: {url}")
        prompt = GptPromptPicker.get_website_health_analysis_prompt(website_url=url)
        response_text = gemini_client.make_request(prompt, url=url)

        if response_text:
            logger.info(f"Received health analysis response for {url}: {response_text}")
            try:
                # Clean the response text by removing markdown code blocks if present
                cleaned_response = clean_json_response(response_text)
                response_json = json.loads(cleaned_response)
                logger.info(f"Parsed health analysis for {url}: {response_json}")

                # Add URL to the response for tracking
                response_json["analyzed_url"] = url
                analysis_results.append(response_json)

            except json.JSONDecodeError as e:
                logger.error(f"Failed to decode JSON from Gemini API response for URL {url}: {e}")
                logger.error(f"Raw response: {response_text}")
                logger.error(f"Cleaned response: {cleaned_response if 'cleaned_response' in locals() else 'N/A'}")

                # Add error entry to results
                analysis_results.append({
                    "analyzed_url": url,
                    "error": "Failed to parse response",
                    "raw_response": response_text
                })
        else:
            logger.warning(f"No response received for URL: {url}")
            analysis_results.append({
                "analyzed_url": url,
                "error": "No response from API"
            })

    return analysis_results