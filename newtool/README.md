# Website Health Analysis API

A production-ready API for analyzing website health, security, and usability using Google's Gemini 2.5 Flash AI model with URL context capabilities.

## Features

- **URL Processing**: Deduplicates, sorts, and processes up to 100 URLs
- **Reachability Testing**: Checks URL accessibility in batches of 10
- **Security Analysis**: Comprehensive security and usability assessment
- **AI-Powered**: Uses Gemini 2.5 Flash with URL context tool and grounding
- **Production Ready**: Includes logging, error handling, and retry logic
- **RESTful API**: FastAPI-based with automatic documentation

## Architecture

### Flow Overview

1. **URL Extraction**: Get all extracted URLs from request
2. **Deduplication**: Remove duplicate URLs
3. **Sorting & Selection**: Sort by length (ascending), take top 100, convert to dictionary
4. **Reachability Check**: Send 10 URLs at once to Gemini, iterate until 4 reachable URLs found
5. **Final Deduplication**: Combine reachable URLs with home page URL (max 5 URLs)
6. **Health Analysis**: Analyze each URL individually for security and usability issues

### Components

- **API Layer** (`api/`): FastAPI endpoints and request models
- **Services** (`services/`): Business logic for reachability and health analysis
- **Utils** (`utils/`): Shared utilities including Gemini client and prompts
- **Logging**: Comprehensive logging throughout the application

## Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd newtool
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables**
```bash
export GEMINI_API_KEY="your-gemini-api-key"
```

## Usage

### Starting the API

```bash
# Using the startup script
python run_api.py

# Or directly with uvicorn
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

### API Endpoints

#### Health Check
```bash
GET /health-check
```

#### Website Health Analysis
```bash
POST /analyze-website-health
```

**Request Body:**
```json
{
  "website": "https://example.com",
  "scrapeRequestRefID": "REQ12345",
  "parsed_urls": [
    {
      "url_depth": 1,
      "urls": ["https://example.com/page1", "https://example.com/page2"]
    },
    {
      "url_depth": 2,
      "urls": ["https://example.com/page3", "https://example.com/page4"]
    }
  ],
  "org_id": "**********"
}
```

**Response:**
```json
{
  "status": "success",
  "results": [
    {
      "analyzed_url": "https://example.com",
      "navigation_issues_exists": "no",
      "navigation_issue_type": [],
      "navigation_issues_area": [],
      "redirection_same_page": "no",
      "links": [],
      "phishing_site": "no",
      "phishing_reason": "",
      "malware_present": "no",
      "malware_reason": "",
      "security_review_present": "no",
      "security_review": "",
      "security_review_source": []
    }
  ]
}
```

## Testing

### Run the test script
```bash
python test_flow.py
```

This will test:
- Gemini client connectivity
- Complete URL processing flow
- Reachability checking
- Health analysis

## Configuration

### Environment Variables

- `GEMINI_API_KEY`: Required. Your Google Gemini API key
- `HOST`: Optional. Server host (default: 0.0.0.0)
- `PORT`: Optional. Server port (default: 8000)
- `RELOAD`: Optional. Enable auto-reload (default: true)

### Gemini 2.5 Flash Features

The application uses:
- **URL Context Tool**: For accessing and analyzing website content
- **Grounding**: For searching security reports and reviews
- **Retry Logic**: 3 attempts with exponential backoff
- **Response Validation**: Comprehensive error handling

## Security Analysis Features

### Navigation Issues
- Broken links (404 errors)
- JavaScript errors
- Login walls
- Infinite redirect loops
- Disabled/dummy links
- Non-standard navigation

### Security Assessment
- Phishing indicators
- Malware risk assessment
- Security threat listings
- User reviews and scam reports

## Production Considerations

- **Logging**: Comprehensive logging with configurable levels
- **Error Handling**: Graceful error handling with detailed error messages
- **Retry Logic**: Automatic retries for API failures
- **Rate Limiting**: Consider implementing rate limiting for production use
- **Monitoring**: Add health checks and monitoring endpoints
- **Security**: Implement authentication and authorization as needed

## API Documentation

When the server is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## License

[Add your license information here]
