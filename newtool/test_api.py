import os
import json
import requests
import subprocess
import time
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("api_test.log"),
        logging.StreamHandler()
    ]
)


def run_tests():
    """Runs the API tests."""
    test_folder = 'test'
    if not os.path.exists(test_folder):
        logging.error(f"Test folder '{test_folder}' not found.")
        return

    json_files = [f for f in os.listdir(test_folder) if f.endswith('.json')]
    if not json_files:
        logging.warning(f"No JSON files found in '{test_folder}'.")
        return

    for json_file in json_files:
        file_path = os.path.join(test_folder, json_file)
        with open(file_path, 'r') as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError:
                logging.error(f"Error decoding JSO<PERSON> from {json_file}")
                continue

        logging.info(f"Sending request for {json_file}...")
        try:
            response = requests.post("http://localhost:8081/analyze-website-health", json=data, timeout=60)
            logging.info(f"Response for {json_file}: {response.status_code}")
            logging.debug(f"Response body for {json_file}: {response.text}")
        except requests.exceptions.RequestException as e:
            logging.error(f"Request for {json_file} failed: {e}")

if __name__ == "__main__":
    run_tests()