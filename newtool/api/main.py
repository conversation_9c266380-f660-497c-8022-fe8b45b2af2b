from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from api.request import WebsiteHealthAnalysisRequest
from utils.url_processor import process_urls
from services.reachability_service import check_url_reachability
from services.health_analysis_service import analyze_website_health
from utils.logger import get_logger

# Initialize FastAPI app with metadata
app = FastAPI(
    title="Website Health Analysis API",
    description="AI-powered website security and usability analysis using Gemini 2.5 Flash",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for production use
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger = get_logger(__name__)

@app.post("/analyze-website-health")
async def analyze_health(request: WebsiteHealthAnalysisRequest):
    """
    Analyzes the health of a website by processing its URLs, checking for
    reachability, and performing a health analysis.

    Returns:
        dict: Analysis results with status and detailed findings
    """
    logger.info(f"Received request for website: {request.website} (RefID: {request.scrapeRequestRefID})")

    # Validate input
    if not request.parsed_urls:
        logger.warning("No URLs provided in request")
        raise HTTPException(status_code=400, detail="No URLs provided for analysis")

    try:
        # 1. Process URLs (dedupe, sort, take top 100, convert to dict)
        logger.info("Step 1: Processing URLs...")
        url_dict = process_urls(request.parsed_urls)
        logger.info(f"Processed {len(url_dict)} unique URLs from {sum(len(item.urls) for item in request.parsed_urls)} total URLs")

        if not url_dict:
            logger.warning("No valid URLs found after processing")
            raise HTTPException(status_code=400, detail="No valid URLs found after processing")

        # 2. Check URL reachability (send 10 at once, iterate until 4 reachable)
        logger.info("Step 2: Checking URL reachability...")
        reachable_urls = check_url_reachability(url_dict, target_reachable_count=4)
        logger.info(f"Found {len(reachable_urls)} reachable URLs out of {len(url_dict)} processed")

        if not reachable_urls:
            logger.warning("No reachable URLs found")
            return {
                "status": "warning",
                "message": "No reachable URLs found",
                "processed_urls": len(url_dict),
                "reachable_urls": 0,
                "results": []
            }

        # 3. Analyze website health (dedupe final list, pass 1 URL at a time)
        logger.info("Step 3: Analyzing website health...")
        analysis_results = analyze_website_health(reachable_urls, request.website)
        logger.info(f"Website health analysis complete for {len(analysis_results)} URLs")

        return {
            "status": "success",
            "processed_urls": len(url_dict),
            "reachable_urls": len(reachable_urls),
            "analyzed_urls": len(analysis_results),
            "results": analysis_results
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during analysis: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during website analysis: {str(e)}"
        )
    
@app.get("/health-check")
async def health_check():
    """
    Health check endpoint to verify API is running.
    """
    return {
        "status": "healthy",
        "service": "Website Health Analysis API",
        "version": "1.0.0",
        "gemini_model": "gemini-2.5-flash"
    }